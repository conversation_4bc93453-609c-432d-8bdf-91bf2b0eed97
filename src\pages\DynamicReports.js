import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  useCallback,
  useContext,
} from "react";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import {
  CloseIcon,
  InfoIcon,
  FilterIcon,
  MenusIcon,
  MailBoxIcon,
  SearchhIcon,
} from "../icons";
import { useNavigate } from "react-router-dom";
import { useLocation } from "react-router-dom";
import Button from "../components/Button/OutlinedButton";
import { CssTooltip } from "../components/StyledComponent";
import { defaultTimeRange, timeAcessOptions } from "../common/constants";
import BreadcrumbNavigation from "../components/BreadCrumps/BreadCrump";
import "react-toastify/dist/ReactToastify.css";
import ReportFilter from "../components/CollapsibleFilter/ReportFilter";
import { MetaDataProvider } from "../context/MetaDataContext";
import ReportCalendar from "../components/DatePicker/ReportCalendar";
import { useMutation, useQuery } from "react-query";
import { getId, previewPanel, downloadPanel } from "../services/panels-api";
import { CircularProgress } from "@mui/material";
import { SelectedFiltersDisplay } from "../common/selectedFiltersDisplay";
import Table from "../components/table/ReportTable";
import DoughnutChart from "../components/charts/DoughnutChart";
import MultiAxisChart from "../components/charts/MultiAxisChart";
import BarChartComponent from "../components/charts/BarCharts";
import LineChartComponent from "../components/charts/LineChart";
import bgImage from "../assets/img/Records.png";
import Pagination from "../components/Pagination/Pagination";
import { DataContext } from "../context/DataContext";
import ResultPerPageComponent from "../components/Pagination/ResultsPerPage";
import ExportPopup from "../popups/exportpopup";
import SendMail from "../popups/SendMail";
import theme from "../tailwind-theme";
import { ToastContainer } from "react-toastify";
import ErrorDialog from "../popups/ErrorDialog";
import SuccessDialog from "../popups/SuccessDialog";

function DynamicReports({ onClose }) {
  const location = useLocation();
  const { value: data, viewBy, activeTab, subtype, id } = location.state || {};
  const [filters, setFilters] = useState([]);
  const [labelData, setLabelData] = useState([]);
  const [limitPerPage, setLimitPerPage] = useState(100);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedRange, setSelectedRange] = useState("Today");
  const [filterDialog, setFilterDialog] = useState(false);
  const [panelById, setPanelById] = useState("");
  const [responseData, setResponseData] = useState([]);
  const [date, setDate] = useState({});
  const [errorMessage, setErrorMessage] = useState(false);
  const [showMenuDropdown, setShowMenuDropdown] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [searchStr, setSearchStr] = useState("");
  const [sendMailDialog, setSendMailDialog] = useState(false);
  const [showExportConfirmation, setShowExportConfirmation] = useState(false);
  const [message, setMessage] = useState("");
  const [extensionType, setExtensionType] = useState("");
  const [errorDialog, setErrorDialog] = useState(false);
  const [successDialog, setSuccessDialog] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState(() => {
    const startDate = dayjs().startOf("day").format("YYYY-MM-DD HH:mm:ss");
    const endDate = dayjs().format("YYYY-MM-DD HH:mm:ss");
    return { startDate, endDate };
  });
  const [payloadData, setPayloadData] = useState({});
  const menuRef = useRef(null);
  const { resultPerPage } = useContext(DataContext);

  // Create separate refs for each chart type
  const pieChartRef = useRef(null);
  const barChartRef = useRef(null);
  const lineChartRef = useRef(null);
  const tableRef = useRef(null);
  const multiAxisRef = useRef(null);

  // Handle search input changes
  const handleKeyUp = (event) => {
    const value = event.target.value;
    setSearchStr(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Export report function
  const exportReport = (type) => {
    setExtensionType(type);
    if (panelById?.visualizationType === "Table Report") {
      handleTableReportDownload();
    } else {
      handleDownloadPDF();
    }
  };

  const handleTableReportDownload = () => {
    if (!panelById) return;

    const selectedRange = panelById.timePeriod;
    let formattedStart = "";
    let formattedEnd = "";
    const currentDateTime = dayjs();

    // Same date formatting logic as in previewById
    if (selectedRange) {
      if (selectedRange.includes("to")) {
        const [startString, endString] = selectedRange.split("to");
        formattedStart = startString;
        formattedEnd = endString;
      } else {
        if (
          selectedRange === "Last Hour" ||
          selectedRange === "Last 6 Hours" ||
          selectedRange === "Last 12 Hours" ||
          selectedRange === "Last 24 Hours"
        ) {
          const hours = {
            "Last Hour": 1,
            "Last 6 Hours": 6,
            "Last 12 Hours": 12,
            "Last 24 Hours": 24,
          };

          const lastXHours = currentDateTime.subtract(
            hours[selectedRange],
            "hour"
          );
          if (selectedRange === "Last Hour") {
            formattedStart = lastXHours.format("YYYY-MM-DD HH:mm:ss");
            formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
          } else {
            formattedStart = lastXHours.format("YYYY-MM-DD HH:00:00");
            formattedEnd = currentDateTime.format("YYYY-MM-DD HH:00:00");
          }
        } else if (selectedRange === "Today") {
          formattedStart = currentDateTime
            .startOf("day")
            .format("YYYY-MM-DD HH:mm:ss");
          formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
        } else if (selectedRange === "Yesterday") {
          const yesterday = currentDateTime.subtract(1, "day");
          formattedStart = yesterday
            .startOf("day")
            .format("YYYY-MM-DD HH:mm:ss");
          formattedEnd = yesterday.endOf("day").format("YYYY-MM-DD HH:mm:ss");
        } else if (selectedRange === "Last Seven Days") {
          formattedStart = currentDateTime
            .subtract(6, "days")
            .startOf("day")
            .format("YYYY-MM-DD HH:00:00");
          formattedEnd = currentDateTime.format("YYYY-MM-DD HH:00:00");
        } else if (selectedRange === "Last Week") {
          formattedStart = currentDateTime
            .subtract(1, "week")
            .startOf("week")
            .format("YYYY-MM-DD HH:00:00");
          formattedEnd = currentDateTime
            .subtract(1, "week")
            .endOf("week")
            .format("YYYY-MM-DD HH:00:00");
        } else if (selectedRange === "Last 30 Days") {
          formattedStart = currentDateTime
            .subtract(29, "days")
            .startOf("day")
            .format("YYYY-MM-DD HH:00:00");
          formattedEnd = currentDateTime
            .endOf("day")
            .format("YYYY-MM-DD HH:00:00");
        } else if (selectedRange === "Last Month") {
          formattedStart = currentDateTime
            .subtract(1, "month")
            .startOf("month")
            .format("YYYY-MM-DD HH:00:00");
          formattedEnd = currentDateTime
            .subtract(1, "month")
            .endOf("month")
            .format("YYYY-MM-DD HH:00:00");
        } else if (selectedRange === "This Month") {
          formattedStart = currentDateTime
            .startOf("month")
            .format("YYYY-MM-DD HH:mm:ss");
          formattedEnd = currentDateTime
            .endOf("day")
            .format("YYYY-MM-DD HH:mm:ss");
        }
      }
    }

    let reqData = {
      reportName: "Dynamic Report",
      name: panelById.name,
      visualizationType: panelById.visualizationType,
      filters: [],
      dataColumns: {
        derivedFields: panelById.dataColumns
          ? panelById.dataColumns.derivedFields
          : [],
        tableFields: panelById?.dataColumns?.tableFields,
      },
      startDate: formattedStart,
      endDate: formattedEnd,
      timezone: timeZone,
      reportFilters: filters, // Include current filters
      download: 1, // Add download parameter
      limit: limitPerPage,
      page: currentPage,
    };

    // Add panel filters
    panelById.filters.forEach((condition) => {
      reqData.filters.push({
        field: condition.field,
        condition: condition.condition,
        value: condition.value,
        operator: condition.operator,
      });
    });

    // Call download API
    downloadPanel({ reqData })
      .then((response) => {
        const url = URL.createObjectURL(response.data);
        const link = document.createElement("a");
        link.href = url;
        const filename = `${data}_TableReport_${
          new Date().toISOString().split("T")[0]
        }.csv`;
        link.download = filename;
        link.click();
        URL.revokeObjectURL(url);
      })
      .catch((error) => {
        console.error("Error downloading table report:", error);
      });
  };

  const handleDownloadPDF = () => {
    const visualizationType = panelById?.visualizationType;

    if (visualizationType === "Table Report") {
      handleTableReportDownload();
      return;
    }

    let targetElement = null;

    // Get the appropriate element based on visualization type
    switch (visualizationType) {
      case "Pie Chart":
        targetElement = pieChartRef.current;
        break;
      case "Bar Graph":
        targetElement = barChartRef.current;
        break;
      case "Line Graph":
        targetElement = lineChartRef.current;
        break;
      case "MultiAxis Graph":
        targetElement = multiAxisRef.current;
        break;
      default:
        console.warn("Unknown visualization type:", visualizationType);
        return;
    }

    if (targetElement) {
      // Create a temporary usePDF instance for this specific element
      import("react-to-pdf").then(({ usePDF }) => {
        const filename = `${data}_${visualizationType?.replace(/\s+/g, "")}_${
          new Date().toISOString().split("T")[0]
        }.pdf`;

        // Use html2canvas and jsPDF for more control
        import("html2canvas").then(({ default: html2canvas }) => {
          import("jspdf").then(({ jsPDF }) => {
            html2canvas(targetElement, {
              scale: 2,
              useCORS: true,
              allowTaint: true,
              backgroundColor: "#ffffff",
            })
              .then((canvas) => {
                const imgData = canvas.toDataURL("image/png");
                const pdf = new jsPDF("l", "mm", "a4");
                const pdfWidth = pdf.internal.pageSize.getWidth();
                const pdfHeight = pdf.internal.pageSize.getHeight();
                const imgWidth = canvas.width;
                const imgHeight = canvas.height;
                const ratio = Math.min(
                  pdfWidth / imgWidth,
                  pdfHeight / imgHeight
                );
                const imgX = (pdfWidth - imgWidth * ratio) / 2;
                const imgY = 30;

                pdf.addImage(
                  imgData,
                  "PNG",
                  imgX,
                  imgY,
                  imgWidth * ratio,
                  imgHeight * ratio
                );
                pdf.save(filename);
              })
              .catch((error) => {
                console.error("Error generating PDF:", error);
              });
          });
        });
      });
    }
  };

  const navigate = useNavigate();
  dayjs.extend(customParseFormat);

  const getUrl = new URL(window.location.href);
  const timeZone = getUrl.search.slice(1);
  const colors = ["#EDDF82", "#82C3ED", "#82EDAD", "#ED8282"];

  function handlePageChange(page) {
    setCurrentPage(page);
  }

  const handleLimitChange = (e) => {
    setCurrentPage(1);
    setLimitPerPage(e?.target?.value);
  };

  const getTimeRange = (viewBy) => {
    if (viewBy.length === 5) {
      return defaultTimeRange;
    } else {
      let newRange = viewBy.map((x) => {
        return timeAcessOptions[x];
      });
      return [...new Set(newRange.flat(1))];
    }
  };

  const filteredKeys = Object.keys(filters).filter(
    (key) => key !== "durationTime"
  );
  const badgeCount = filteredKeys.length;

  useEffect(() => {
    function handleClickOutside(event) {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setShowMenuDropdown(false);
      }
    }

    if (showMenuDropdown) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showMenuDropdown]);

  const { mutate: previewPanelAPIData, isLoading: loadingData } =
    useMutation(previewPanel);

  useQuery(["getPanelById", id], getId, {
    enabled: !!id,
    onSuccess: ({ data }) => {
      setPanelById(data);
      setSelectedRange(data?.timePeriod);
      previewById(data);
    },
    refetchOnWindowFocus: false,
  });

  const previewById = useCallback(
    (data, reportFilters = {}, isDownload = false) => {
      //console.log("datavalue", data);
      const selectedRange = data.timePeriod;

      let formattedStart = "";
      let formattedEnd = "";
      const currentDateTime = dayjs();

      if (selectedRange) {
        if (selectedRange.includes("to")) {
          const [startString, endString] = selectedRange.split("to");
          formattedStart = startString;
          formattedEnd = endString;
        } else {
          if (
            selectedRange === "Last Hour" ||
            selectedRange === "Last 6 Hours" ||
            selectedRange === "Last 12 Hours" ||
            selectedRange === "Last 24 Hours"
          ) {
            const hours = {
              "Last Hour": 1,
              "Last 6 Hours": 6,
              "Last 12 Hours": 12,
              "Last 24 Hours": 24,
            };

            const lastXHours = currentDateTime.subtract(
              hours[selectedRange],
              "hour"
            );
            if (selectedRange === "Last Hour") {
              formattedStart = lastXHours.format("YYYY-MM-DD HH:mm:ss");
              formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
            } else {
              formattedStart = lastXHours.format("YYYY-MM-DD HH:00:00");
              formattedEnd = currentDateTime.format("YYYY-MM-DD HH:00:00");
            }
          } else if (selectedRange === "Today") {
            formattedStart = currentDateTime
              .startOf("day")
              .format("YYYY-MM-DD HH:mm:ss");
            formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
          } else if (selectedRange === "Yesterday") {
            const yesterday = currentDateTime.subtract(1, "day");
            formattedStart = yesterday
              .startOf("day")
              .format("YYYY-MM-DD HH:mm:ss");
            formattedEnd = yesterday.endOf("day").format("YYYY-MM-DD HH:mm:ss");
          } else if (selectedRange === "Last Seven Days") {
            formattedStart = currentDateTime
              .subtract(6, "days")
              .startOf("day")
              .format("YYYY-MM-DD HH:00:00");
            formattedEnd = currentDateTime.format("YYYY-MM-DD HH:00:00");
          } else if (selectedRange === "Last Week") {
            formattedStart = currentDateTime
              .subtract(1, "week")
              .startOf("week")
              .format("YYYY-MM-DD HH:00:00");
            formattedEnd = currentDateTime
              .subtract(1, "week")
              .endOf("week")
              .format("YYYY-MM-DD HH:00:00");
          } else if (selectedRange === "Last 30 Days") {
            formattedStart = currentDateTime
              .subtract(29, "days")
              .startOf("day")
              .format("YYYY-MM-DD HH:00:00");
            formattedEnd = currentDateTime
              .endOf("day")
              .format("YYYY-MM-DD HH:00:00");
          } else if (selectedRange === "Last Month") {
            formattedStart = currentDateTime
              .subtract(1, "month")
              .startOf("month")
              .format("YYYY-MM-DD HH:00:00");
            formattedEnd = currentDateTime
              .subtract(1, "month")
              .endOf("month")
              .format("YYYY-MM-DD HH:00:00");
          } else if (selectedRange === "This Month") {
            formattedStart = currentDateTime
              .startOf("month")
              .format("YYYY-MM-DD HH:mm:ss");
            formattedEnd = currentDateTime
              .endOf("day")
              .format("YYYY-MM-DD HH:mm:ss");
          }
        }
      }

      let reqData = {
        reportName: "Dynamic Report",
        name: data.name,
        visualizationType: data.visualizationType,
        filters: [],
        dataColumns: {
          derivedFields: data.dataColumns ? data.dataColumns.derivedFields : [],
        },
        startDate: formattedStart,
        endDate: formattedEnd,
        timezone: timeZone,
        reportFilters: reportFilters, // Add the report filters from the filter dialog
        search: searchStr, // Add search parameter
      };

      // Add download parameter for Table Reports when downloading
      if (isDownload && data.visualizationType === "Table Report") {
        reqData.download = 1;
      }

      if (data.visualizationType === "Bar Graph") {
        reqData.dataColumns["X-Axis"] = data.dataColumns["X-Axis"];
        reqData.dataColumns.noOfRecords = parseInt(
          data.dataColumns.noOfRecords
        );
      } else {
        reqData.dataColumns.tableFields = data?.dataColumns?.tableFields;
      }
      if (
        data.visualizationType === "Line Graph" ||
        data.visualizationType === "MultiAxis Graph"
      ) {
        reqData.interval = data.interval;
      }
      if (data.visualizationType === "Table Report") {
        reqData.limit = limitPerPage;
        reqData.page = currentPage;
      }
      data.filters.forEach((condition) => {
        reqData.filters.push({
          field: condition.field,
          condition: condition.condition,
          value: condition.value,
          operator: condition.operator,
        });
      });
      setPayloadData(reqData);
      // console.log("formattedStart", formattedStart);
      previewPanelAPIData(
        {
          reqData,
        },
        {
          onSuccess: ({ data }) => {
            setErrorMessage(false);
            setResponseData(data);
            setDate({ startDate: formattedStart, endDate: formattedEnd });
            //console.log("date123", date);
          },
          onError: (error) => {
            setErrorMessage(true);
          },
        }
      );
    },
    [previewPanelAPIData, limitPerPage, currentPage, timeZone, searchStr]
  );

  useEffect(() => {
    if (panelById) {
      if (Object.keys(filters).length > 0) {
        previewById(panelById, filters);
      } else {
        previewById(panelById, {});
      }
    }
  }, [filters, panelById, previewById, currentPage, limitPerPage, searchStr]);

  const columns = useMemo(() => {
    if (panelById?.visualizationType === "Table Report") {
      if (!responseData?.data || responseData?.data?.length === 0) {
        return [];
      }

      const firstItem = responseData?.data[0];
      if (!firstItem) {
        return [];
      }

      const keys = Object.keys(firstItem);

      const dynamicColumns = keys.map((key) => ({
        header: key,
        accessorKey: key,
        Cell: ({ row }) => {
          const value = row.original[key];
          if (key === "Date") {
            return dayjs(value, { format: "YYYY-MM-DDTHH:mm:ss" }).isValid()
              ? dayjs(value).format("DD/MM/YYYY HH:mm:ss")
              : value;
          }
          return value;
        },
      }));

      return dynamicColumns;
    }
    return [];
  }, [responseData?.data, panelById?.visualizationType]);

  return (
    <>
      {/* Sticky Header with Breadcrumb */}
      <div className="sticky top-0 z-10 w-full bg-bgPrimary flex items-start text-headingColor text-2xl font-bold leading-tight">
        <BreadcrumbNavigation
          linkTwo="Static Reports"
          onlinkTwoClick={() =>
            navigate("/app/reports", {
              state: { tab: activeTab, subType: subtype },
            })
          }
          title={data}
        />
      </div>

      {/* Main Content Container */}
      <div className="bg-white p-3">
        {/* Close Icon Section */}
        <div className="flex justify-end items-center">
          <CloseIcon
            onClick={() =>
              navigate("/app/reports", {
                state: { tab: activeTab, subType: subtype },
              })
            }
            className="w-2 h-2 cursor-pointer mb-1"
          />
        </div>

        {/* Search and Controls */}
        <div className="mx-3 flex flex-wrap items-center justify-between gap-y-3">
          {/* Search Input */}
          <div className="w-full md:w-[300px] relative">
            <input
              type="text"
              style={{
                border: `1px solid ${theme.borderColor.outerBorder}`,
                paddingLeft: "2.5rem",
              }}
              className="w-full text-tabColor bg-white rounded-md focus:outline-none text-sm h-10"
              placeholder="Search"
              value={searchStr}
              onChange={handleKeyUp}
            />
            <div className="absolute top-3 left-3">
              <SearchhIcon className="w-4 h-4" />
            </div>
          </div>

          {/* Calendar, Info Tooltip, Mail, Filter, Refresh, Download */}
          <div className="flex items-center space-x-8">
            {/* Menu Icon with Dropdown - Now positioned first */}
            <div className="relative" ref={menuRef}>
              <div
                className="rounded-full bg-bgouterBackground p-2 flex items-center justify-center cursor-pointer"
                onClick={() => {
                  setShowMenuDropdown(!showMenuDropdown);
                  setOpenDialog(false);
                }}
              >
                <CssTooltip title={"Report Menu"} placement="top" arrow>
                  <MenusIcon className="w-5 h-5" />
                </CssTooltip>
              </div>

              {/* Dropdown Menu */}
              {showMenuDropdown && (
                <div className="absolute right-0 mt-2 w-72 bg-white rounded-md shadow-lg z-10 border border-gray-200 p-3">
                  <div className="grid grid-cols-2 gap-2">
                    {/* Send Mail Option */}
                    <div
                      className="flex items-center gap-1 cursor-pointer p-2 rounded hover:bg-gray-100"
                      onClick={() => {
                        setSendMailDialog(true);
                        setShowMenuDropdown(false);
                      }}
                    >
                      <MailBoxIcon className="w-5 h-5" />
                      <span className="text-sm text-gray-700 ml-2">
                        Send Email
                      </span>
                    </div>

                    {/* Filters Option */}
                    <div
                      className={`flex items-center gap-1 cursor-pointer p-2 rounded hover:bg-gray-100`}
                      onClick={() => {
                        setFilterDialog(true);
                        setShowMenuDropdown(false);
                      }}
                    >
                      <div className="relative">
                        <FilterIcon className="w-5 h-5" />
                        {badgeCount > 0 && (
                          <CssTooltip
                            title={
                              labelData.length > 0 ? labelData.join(", ") : ""
                            }
                            placement="top"
                            arrow
                          >
                            <div className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full h-4 w-4 flex items-center justify-center text-xs font-medium">
                              {badgeCount}
                            </div>
                          </CssTooltip>
                        )}
                      </div>
                      <span className="text-sm text-gray-700 ml-2 truncate">
                        Filters
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Info Tooltip */}
            <CssTooltip
              title={
                <div className="text-xs p-1">
                  <p className="mb-1.5">
                    During calendar selection, to view reports spanning more
                    than an hour, a day, a week, or a month, make sure to set
                    the start time to 00:00.
                  </p>
                  <p className="mb-1.5">
                    In case of selection of last 6, 12, 24 hours; reports will
                    be generated on an hourly basis with the start time of the
                    selected hour.
                  </p>
                  <p className="mb-1.5">
                    For example, if the current user time range is from 13:01 to
                    13:59, then the report will be generated from 13:00 hour
                    onwards.
                  </p>
                </div>
              }
              placement="top"
              arrow
            >
              <InfoIcon className="ml-2 mt-1 w-4 h-3.5" />
            </CssTooltip>

            {/* Calendar Picker - Disabled for weekly/monthly */}
            <div
              className={`${
                filters?.duration === "weekly" ||
                filters?.duration === "monthly"
                  ? "pointer-events-none opacity-50"
                  : ""
              }`}
              onClick={() => {
                setShowMenuDropdown(false);
              }}
            >
              <ReportCalendar
                selectedFilter={selectedFilter}
                setSelectedFilter={setSelectedFilter}
                setSelectedRange={setSelectedRange}
                selectedRange={selectedRange}
                reportTimeRange={getTimeRange(viewBy)}
                viewBy={viewBy}
                subtype={subtype}
                openDialog={openDialog}
                setOpenDialog={setOpenDialog}
                data={data}
                isAdmin={false}
              />
            </div>

            {/* Download Button */}
            <Button
              buttonClassName="text-xs w-32 text-white h-10 rounded-md"
              label="Download"
              onClick={() => setShowExportConfirmation(true)}
              disabled={loadingData}
            />
          </div>
        </div>

        {loadingData && (
          <div className="mt-3 bg-white p-5 w-[65vw]">
            <div className="my-5 flex justify-center items-center">
              <CircularProgress size={40} />
            </div>
          </div>
        )}

        {!loadingData && responseData && panelById && (
          <>
            {" "}
            <div className="font-semibold flex-grow flex items-center justify-between mb-3 m-5">
              <div className="text-sm">
                {"From :"} {date?.startDate || ""}
                {" - "}
                {"To :"} {date?.endDate || ""}
              </div>
              <div className="flex font-hebrew text-sm whitespace-nowrap">
                <div className="flex">
                  Selected Filters:
                  <CssTooltip
                    title={
                      <SelectedFiltersDisplay conditions={panelById?.filters} />
                    }
                    placement="bottom"
                    arrow
                  >
                    <InfoIcon className="ml-2 w-4 mt-1 h-3.5" />
                  </CssTooltip>
                </div>
              </div>
            </div>
            {errorMessage ? (
              <div className="border border-outerBorder mb-5 mt-8">
                <div className="flex text-headingColor text-2xl justify-center font-bold mt-5">
                  Oops! No records to display.
                </div>
                <div className="flex justify-center my-10">
                  <img
                    src={bgImage}
                    className="h-[10%] w-[10%] object-cover"
                    alt="bg"
                  />
                </div>
              </div>
            ) : (
              <div
                className={`${
                  panelById.visualizationType === "Table Report"
                    ? "m-3"
                    : "md:flex gap-5 border border-outerBorder m-5 p-3"
                }`}
              >
                {panelById.visualizationType === "Pie Chart" ? (
                  <div
                    ref={pieChartRef}
                    className="pie-chart-container"
                    style={{
                      display: "flex",
                      //justifyContent: "space-between",
                      width: "100%",
                      height: "300px",
                      margin: "auto",
                    }}
                  >
                    <DoughnutChart
                      className={"mx-auto"}
                      respData={responseData?.data || []}
                      reportData={true}
                    />
                  </div>
                ) : panelById.visualizationType === "Bar Graph" ? (
                  <div
                    ref={barChartRef}
                    className="bar-chart-container"
                    style={{
                      display: "flex",
                      //justifyContent: "space-between",
                      width: "100%",
                      height: "450px",
                      margin: "auto",
                    }}
                  >
                    <BarChartComponent
                      className={"mx-auto"}
                      chartData={responseData?.data || []}
                    />
                  </div>
                ) : panelById.visualizationType === "Line Graph" ? (
                  <div
                    ref={lineChartRef}
                    className="line-chart-container"
                    style={{
                      display: "flex",
                      width: "100%",
                      height: "450px",
                      margin: "auto",
                    }}
                  >
                    <LineChartComponent
                      data={responseData?.data}
                      colors={colors}
                      // isPreview={isPreview}
                    />
                  </div>
                ) : panelById.visualizationType === "Table Report" ? (
                  <div ref={tableRef} className="table-container">
                    <Table data={responseData?.data || []} columns={columns} />
                    <div className="flex items-center justify-between mt-5">
                      <div className="flex items-center">
                        <ResultPerPageComponent
                          countPerPage={resultPerPage}
                          limit={limitPerPage}
                          handleLimitChange={handleLimitChange}
                          pageName="reports"
                        />
                        <div className="text-sm pl-3 text-titleColor">
                          {(currentPage - 1) * limitPerPage + 1} -{" "}
                          {Math.min(
                            limitPerPage * currentPage,
                            responseData?.totalCount
                          )}{" "}
                          of {responseData?.totalCount} rows
                        </div>
                      </div>

                      <Pagination
                        className="pagination-bar"
                        currentPage={currentPage}
                        totalCount={responseData?.totalCount}
                        pageSize={limitPerPage}
                        onPageChange={handlePageChange}
                      />
                    </div>
                  </div>
                ) : panelById.visualizationType === "MultiAxis Graph" ? (
                  <div
                    ref={multiAxisRef}
                    className="multi-axis-chart-container"
                    style={{
                      display: "flex",
                      //justifyContent: "space-between",
                      width: "100%",
                      height: "450px",
                      margin: "auto",
                    }}
                  >
                    <MultiAxisChart data={responseData || []} />{" "}
                  </div>
                ) : null}
              </div>
            )}
          </>
        )}
      </div>

      {/* Export Report Modal */}
      <ExportPopup
        show={showExportConfirmation}
        onHide={() => setShowExportConfirmation(false)}
        onConfirm={(type) => {
          exportReport(type);
          setShowExportConfirmation(false);
        }}
        title={"Export Report"}
        identity={"Reports"}
        exportPermissions={{ csv: true, excel: true, pdf: true }}
      />

      {/* Send Mail Dialog */}
      <SendMail
        openGroupDialog={sendMailDialog}
        closeGroupDialog={() => {
          setSendMailDialog(false);
        }}
        selectedFilter={selectedFilter}
        searchStr={searchStr}
        type={extensionType}
        reportName={data}
        timeZone={timeZone}
        payloadData={payloadData}
        dynamicReports={true}
      />

      {/* Toast Notifications */}
      <ToastContainer position="top-center" autoClose={3000} />

      {/* Error and Success Dialogs */}
      <ErrorDialog
        show={errorDialog}
        onHide={() => setErrorDialog(false)}
        message={message}
      />
      <SuccessDialog
        show={successDialog}
        onHide={() => setSuccessDialog(false)}
        message={message}
      />

      {/* Conditional Filter Dialog */}
      {filterDialog && (
        <MetaDataProvider>
          <ReportFilter
            openFilterDialog={filterDialog}
            closeFilterDialog={() => {
              setFilterDialog(false);
            }}
            reportName={"dynamicReports"}
            setFilters={setFilters}
            filterData={filters}
            setLabelData={setLabelData}
            visualizationType={panelById?.visualizationType}
          />
        </MetaDataProvider>
      )}
    </>
  );
}

export default DynamicReports;
