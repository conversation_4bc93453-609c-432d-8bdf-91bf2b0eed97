import { Formik, Form, Field, ErrorMessage } from "formik";

import Table from "../../components/table/Table";

import { useContext, useEffect, useMemo, useRef, useState } from "react";
import { roleService } from "../../services/roles.service";
import * as Yup from "yup";
import { useNavigate } from "react-router-dom";
import { reportService } from "../../services/staticreport.service";
import { Link } from "react-router-dom";
import { nameValidation } from "../../common/yupValidation";
import CustomTab from "../customTab/CustomTab";
import { Alert, InfoIcon } from "../../icons";
import InfoModal from "../modals/InfoModal";
import CustomSwitch from "../Toggle-switch/switch";
import SuccessDialog from "../../popups/SuccessDialog";
import "react-toastify/dist/ReactToastify.css";
import { toast, ToastContainer } from "react-toastify";
// import { cardServices } from "../../services/cards-api";
import { getAlltoRole } from "../../services/dashboard-api";
import { role_management_constants } from "../../utils/constants";
import { AuthContext } from "../../context/AuthContext";
import { ConfigService } from "../../services/config-api";
import InputLabel from "../FormsUI/InputLabel/InputLabel";
import Button from "../Button/OutlinedButton";
import CancelButton from "../Button/Button";
import Pagination from "../Pagination/Pagination";
import { CssTooltip } from "../../components/StyledComponent";

var style = {
  asterisk: {
    color: "red",
    marginLeft: "3px",
  },
  fieldstyle:
    "form-control  w-3/4 mt-2 text-sm pl-5 text-black bg-white bg-clip-padding border border-solid border-Bittersweet rounded transition ease-in-out  m-0 inline-block h-10  font-semibold ",
};

const tabItems = [
  { label: "Basic Features" },
  { label: "Static Reports" },
  { label: "Dynamic Dashboard" },
  // { label: "Cards" },
];

const RolesForm = ({ editRoleItem, isEditPage, values }) => {
  const navigate = useNavigate();
  const effectRef = useRef(null);
  const { roles, configApiData } = useContext(AuthContext);
  // eslint-disable-next-line
  const [suppliers, setSuppliers] = useState([]);
  // eslint-disable-next-line
  const [customers, setCustomers] = useState([]);
  // eslint-disable-next-line
  const [status, setStatus] = useState("");
  const [submitting, setSubmitting] = useState(false);
  const [dashBoardCount, setDashBoardCount] = useState(0);
  const [panelCount, setPanelCount] = useState(0);
  const [initialValues, setInitialValues] = useState(
    role_management_constants.initial_values
  );
  /// const [dashBoardCount, setDashBoardCount] = useState(0);
  //const [panelCount, setPanelCount] = useState(0);

  const [selectedTab, setSelectedTab] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isReportPopup, setIsReportPopup] = useState(false);
  const [isDashboardPopup, setIsDashboardPopup] = useState(false);
  const [showAlertConfirmation, setShowAlertConfirmation] = useState(0);
  const [enableDashboardCount, setEnableDashboardCount] = useState(true);
  const [enablePanelCount, setEnablePanelCount] = useState(true);

  const [selectAllObject, setSelectAllObject] = useState({
    resources: false,
    reports: false,
    dashboard: [false],
  });

  const [successDialog, setSuccessDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [resourceArray, setResourceArray] = useState(
    role_management_constants.resource
  );
  const [reportArray, setReportArray] = useState([]);
  const [dashboardArray, setDashboardArray] = useState([]);
  const [popupMessage, setPopupMessage] = useState("");
  const [dashboardPage, setDashboardPage] = useState(0);
  const [isCreatePopup, setIsCreatePopup] = useState(false);
  const formikRef = useRef(null);

  const handleSelectAll = (val, e, key) => {
    //resources
    const selectedValues = {
      "Card Management": ["create", "view", "update", "delete"],
      "Logs Management": ["view"],
      //"Role Management": ["create", "view", "update", "delete"],
      // "User Management": ["create", "view", "update", "delete"],
      "Alert Management": ["create", "view", "update", "delete"],
      "Group Management": ["create", "view", "update", "delete"],
      "Panel Management": ["create", "view", "update", "delete"],
      "Report Management": ["view"],
      "Dashboard Management": ["create", "view", "update", "delete"],
      "Default Dashboard": ["view"],
      "CDR Search": ["view", "download"],
    };

    if (key === "resources") {
      formikRef.current.setFieldValue(
        "resources",
        val
          ? { ...selectedValues }
          : {
              "Card Management": [],
              "Logs Management": ["view"],
              "Alert Management": [],
              "Group Management": [],
              "Panel Management": [],
              "Report Management": [],
              "Dashboard Management": [],
              "Default Dashboard": ["view"],
            }
      );
      setSelectAllObject((oldState) => ({ ...oldState, resources: val }));
    } else if (key === "reports") {
      let newReport = {};
      if (
        !formikRef.current.values["resources"]["Report Management"]?.includes(
          "view"
        )
      ) {
        setShowAlertConfirmation(1);
        setPopupMessage(
          "Please provide view option for report management in basic features to select reports"
        );
        return;
      }

      reportArray.forEach((ele) => {
        newReport[ele.name] = [];
        if (val) {
          Object.keys(ele.permissions).forEach((ent) =>
            newReport[ele.name].push(ent)
          );
        }
      });
      formikRef.current.setFieldValue("staticReports", { ...newReport });
      setSelectAllObject((oldState) => ({ ...oldState, reports: val }));
    } else if (key === "dashboard") {
      if (
        !formikRef.current.values["resources"][
          "Dashboard Management"
        ]?.includes("view")
      ) {
        setShowAlertConfirmation(1);
        setPopupMessage(
          "Please provide view option for dashboard management in basic features to select dashboards"
        );
        return;
      }
      let newDashboard = formikRef.current.values["dynamicDashboard"];
      dashboardArray.forEach((ele, i) => {
        if (i < dashboardPage * 10 || i > dashboardPage * 10 + 10 - 1) return;
        newDashboard[ele.name] = [];
        if (val) {
          Object.keys(ele.permissions).forEach((ent) =>
            newDashboard[ele.name].push(ent)
          );
        }
      });
      formikRef.current.setFieldValue("dynamicDashboard", { ...newDashboard });
      let newDash = selectAllObject.dashboard;
      newDash[dashboardPage] = val;
      setSelectAllObject((oldState) => ({ ...oldState, dashboard: newDash }));
    }
    return;
  };

  //eslint-disable-next-line
  const getCustomers = () => {
    const customers = [];
    for (let i = 1; i <= 20; i++) {
      customers.push("Customer " + i);
    }
    setCustomers(customers);
  };

  //eslint-disable-next-line
  const getSuppliers = () => {
    const suppliers = [];
    for (let i = 1; i <= 20; i++) {
      suppliers.push("Supplier " + i);
    }
    setSuppliers(suppliers);
  };

  useEffect(() => {
    const getRoleDetailsAPI = () => {
      roleService.getById(editRoleItem).then(({ data }) => {
        setResourceArray(data.resources);
        setReportArray(data.staticReports);

        let resources = {};
        if (data.resources.length) {
          data.resources.forEach((item) => {
            let res = [];
            Object.keys(item.permissions).forEach((key) => {
              if (item.permissions[key]) res.push(key);
            });
            resources[item.name] = res;
          });
        }
        let dynamicDashboard = {};
        try {
          data.dynamicDashboard.forEach((ele, i) => {
            let res = [];
            Object.keys(ele.permissions).forEach((key) => {
              if (ele.permissions[key]) res.push(key);
            });
            dynamicDashboard[ele.name] = res;
          });
        } catch (error) {
          console.log("error: ", error.message);
        }

        setDashboardArray(data.dynamicDashboard);
        formikRef.current.setFieldValue("dynamicDashboard", {
          ...dynamicDashboard,
        });

        if (resources["Report Management"]?.includes("view")) {
          setIsReportPopup(false);
        }
        if (resources["Dashboard Management"]?.includes("view")) {
          setIsDashboardPopup(false);
        }
        if (resources["Dashboard Management"]?.includes("create")) {
          setEnableDashboardCount(false);
        }

        if (resources["Panel Management"]?.includes("create")) {
          setEnablePanelCount(false);
        }

        let staticReports = {};

        try {
          data.staticReports.forEach((ele) => {
            let res = [];
            Object.keys(ele.permissions).forEach((key) => {
              if (ele.permissions[key]) res.push(key);
            });
            staticReports[ele.name] = res;
          });
        } catch (error) {
          console.log("error: ", error.message);
        }

        setDashBoardCount(data.dashboardCount);
        setPanelCount(data.panelCount);
        setInitialValues((state) => ({
          ...state,
          name: data.name,
          resources,
          staticReports,
          dynamicDashboard,
          //  key: initialValues.key + 1,

          //  staticReportsArray,
        }));
        const checkDashboardSelection = (page, array, data) => {
          const dashboards = array?.slice(page * 10, page * 10 + 10);
          for (const ele of dashboards) {
            if (
              !data[ele.name].includes("view") ||
              !data[ele.name].includes("download")
            ) {
              return false;
            }
          }
          return true;
        };

        let newState = {
          dashboard: [],
          resources: handleSingleCheckboxUnSelection({ resources }),
          reports: handleSingleCheckboxUnSelection(
            { staticReports },
            false,
            true
          ),
        };
        for (let i = 0; i < data.dynamicDashboard.length; i++) {
          newState.dashboard.push(
            checkDashboardSelection(i, data.dynamicDashboard, dynamicDashboard)
          );
        }
        // newState.dashboard[dashboardPage] = handleSingleCheckboxUnSelection(
        //   { dynamicDashboard },
        //   false
        // );

        setSelectAllObject((oldState) => ({ ...oldState, ...newState }));
      });
    };

    //  getCustomers();
    //getSuppliers();
    const getStuffs = () => {
      reportService.getReportDetails("REPORT_SUB_TYPE", "").then(({ data }) => {
        const transformedData = {};
        Object.values(data).forEach((categoryReports) => {
          categoryReports.forEach((report) => {
            transformedData[report.name] = [];
          });
        });

        let staticReports = {};
        const tempStaticReports = [];
        Object.keys(transformedData).forEach((reportName) => {
          tempStaticReports.push({
            name: reportName,
            permissions: {
              view: 0,
              download: 0,
            },
          });
          staticReports[reportName] = [];
        });

        setReportArray(tempStaticReports);
        setInitialValues((state) => ({
          ...state,
          staticReports,
          key: state.key + 1,
        }));
      });
    };

    if (effectRef.current === null) {
      if (isEditPage) {
        getRoleDetailsAPI();
      } else {
        getStuffs();
        // getAllCards();
      }
      effectRef.current = true;
    }
    //eslint-disable-next-line
  }, [isEditPage]);

  useEffect(() => {
    const getAllDashboard = () => {
      getAlltoRole().then(({ data }) => {
        const sampleDasboards = [];
        let dynamicDashboard = {};
        data.data.forEach((item) => {
          dynamicDashboard[item.name] = [];
          sampleDasboards.push({
            name: item.name,
            permissions: {
              view: 0,
              download: 0,
            },
          });
        });

        setDashboardArray(sampleDasboards);
        setInitialValues((state) => ({
          ...state,
          dynamicDashboard: { ...dynamicDashboard },
          key: state.key + 1,
        }));
        formikRef.current.setFieldValue("dynamicDashboard", {
          ...dynamicDashboard,
        });
      });
    };
    if (!isEditPage) getAllDashboard();
  }, [isEditPage]);

  const handleSubmit = (values) => {
    if (isEditPage) {
      values.dashboardCount = dashBoardCount;
      values.panelCount = panelCount;

      setSubmitting(false);
      roleService
        .create(values, editRoleItem) //for update roles
        .then((res) => {
          if (res.data.status === "OK") {
            setMessage("Role Updated Successfully");
            setSuccessDialog(true);
            // window.location.reload();
            setSubmitting(false);
          }
          // setShowDeleteConfirmation(0);
        })
        .catch((error) => {
          setSubmitting(false);
          toast.error(
            error?.response?.data?.message
              ? error.response.data.message
              : error.message
          );
        });
    } else {
      values.dashboardCount = dashBoardCount;
      values.panelCount = panelCount;

      setSubmitting(false);
      roleService
        .create(values)
        .then((res) => {
          if (res.data.status === "OK") {
            setMessage("Role Added Successfully");
            setSuccessDialog(true);
            // window.location.reload();
            setSubmitting(false);
          }
          // setShowDeleteConfirmation(0);
        })
        .catch((error) => {
          setSubmitting(false);
          toast.error(
            error.response.data.message ? error.response.data.message : error
          );
        });
    }
  };

  const handleTabChange = (event, newValue) => {
    if (isCreatePopup) {
      setShowAlertConfirmation(1);
      setPopupMessage(
        "Card or Panel create is mandatory if dashboard management create is enabled in resources"
      );
    } else {
      event.preventDefault();
      setSelectedTab(newValue);
      setIsLoading(true);
      // if (onChange) {
      //   onChange(newValue);
      // }
      setTimeout(() => {
        setIsLoading(false);
      }, 100);
    }
  };

  const ConfigCall = () => {
    ConfigService.getConfigApi()
      .then((data) => {
        //console.log("result", data.data);
      })
      .catch((error) => {
        //console.error("result-error", error);
      });
  };

  useEffect(() => {
    ConfigCall();
  }, []);

  const handleValidateCall = (value) => {
    if (value?.resources["Dashboard Management"]?.includes("create")) {
      setEnableDashboardCount(false);
      if (dashBoardCount === 0) {
        setDashBoardCount(1);
      }
      if (
        value?.resources["Panel Management"]?.includes("create") &&
        panelCount === 0 &&
        value?.resources["Dashboard Management"]?.includes("create")
        // dashBoardCount !== 0
      ) {
        setPanelCount(1);
      }
      if (!value?.resources["Panel Management"]?.includes("create")) {
        setEnablePanelCount(true);
      }
      if (
        value?.resources["Panel Management"]?.includes("create") ||
        value?.resources["Card Management"]?.includes("create")
      ) {
        setIsCreatePopup(false);

        if (value?.resources["Panel Management"]?.includes("create")) {
          setEnablePanelCount(false);
        }
      } else {
        setIsCreatePopup(true);
      }
    } else {
      setEnableDashboardCount(true);
      setDashBoardCount(0);
      setIsCreatePopup(false);
    }

    if (!value?.resources["Panel Management"]?.includes("create")) {
      setPanelCount(0);
    }
    if (value?.resources["Report Management"]?.includes("view")) {
      setIsReportPopup(false);
    }
    if (value?.resources["Dashboard Management"]?.includes("view")) {
      setIsDashboardPopup(false);
    }

    if (!value?.resources["Report Management"]?.includes("view")) {
      setIsReportPopup(true);
      setPopupMessage(
        "Please provide view option for report management in basic features to select reports"
      );
      setSelectAllObject((oldState) => ({ ...oldState, reports: false }));

      Object.keys(value.staticReports).forEach((key) => {
        value.staticReports[key] = [];
      });
    }
    if (!value?.resources["Dashboard Management"]?.includes("view")) {
      let newDash = selectAllObject.dashboard;
      newDash[dashboardPage] = false;
      setSelectAllObject((oldState) => ({ ...oldState, dashboard: newDash }));

      setPopupMessage(
        "Please provide view option for dashboard management in basic features to select dashboards"
      );
      setIsDashboardPopup(true);
      Object.keys(value.dynamicDashboard).forEach((key) => {
        value.dynamicDashboard[key] = [];
      });
    }

    switch (selectedTab) {
      case 0:
        setSelectAllObject((oldState) => ({
          ...oldState,
          resources: handleSingleCheckboxUnSelection(value, true),
        }));

        break;
      case 1:
        setPopupMessage(
          "Please provide view option for report management in basic features to select reports"
        );
        setSelectAllObject((oldState) => ({
          ...oldState,
          reports: handleSingleCheckboxUnSelection(value, false),
        }));
        break;
      case 2:
        setPopupMessage(
          "Please provide view option for dashboard management in basic features to select dashboards"
        );
        let newDash = selectAllObject.dashboard;
        newDash[dashboardPage] = handleSingleCheckboxUnSelection(value, false);
        setSelectAllObject((oldState) => ({
          ...oldState,
          dashboard: newDash,
        }));

        break;
      default:
        break;
    }
  };

  const handleSingleCheckboxUnSelection = (
    value,
    isResources = true,
    isAllCheck = false
  ) => {
    if (isResources) {
      const resources = value.resources;
      for (const key in resources) {
        if (
          key === "Logs Management" ||
          key === "Report Management" ||
          key === "Default Dashboard"
        ) {
          if (!resources[key]?.includes("view")) {
            return false;
          }
        } else if (key === "CDR Search") {
          if (
            !resources[key]?.includes("view") ||
            !resources[key]?.includes("download")
          ) {
            return false;
          }
        } else {
          if (
            !resources[key]?.includes("create") ||
            !resources[key]?.includes("view") ||
            !resources[key]?.includes("update") ||
            !resources[key]?.includes("delete")
          ) {
            return false;
          }
        }
      }
      return true;
    } else {
      if (selectedTab === 1 || isAllCheck) {
        const reports = value.staticReports;
        for (const key in reports) {
          if (
            !reports[key].includes("view") ||
            !reports[key].includes("download")
          ) {
            return false;
          }
        }
        return true;
      } else {
        if (dashboardArray?.length > 0) {
          const dashboards = dashboardArray?.slice(
            dashboardPage * 10,
            dashboardPage * 10 + 10
          );

          for (const ele of dashboards) {
            if (
              !value.dynamicDashboard[ele.name].includes("view") ||
              !value.dynamicDashboard[ele.name].includes("download")
            ) {
              return false;
            }
          }
          return true;
        } else {
          return false;
        }
      }
    }
  };

  const handleReportCheckboxClick = (e) => {
    if (isReportPopup) {
      setShowAlertConfirmation(1);
      setPopupMessage(
        "Please provide view option for report management in basic features to select reports"
      );
    }
  };
  const handleDashboardCheckboxClick = (e) => {
    if (isDashboardPopup) {
      setShowAlertConfirmation(1);
      setPopupMessage(
        "Please provide view option for dashboard management in basic features to select dashboards"
      );
    }
  };

  const handleSuccessDialog = () => {
    navigate("/app/rolemanagement");
    setSuccessDialog(false);
  };

  const resourceColumns = useMemo(
    () => [
      {
        Header: () => (
          <div className="flex items-center text-sm font-bold">
            <span style={{ paddingLeft: "10px" }}>Resource</span>
            {/* <SortIcon className="w-3 h-3 ml-5" /> */}
          </div>
        ),
        accessor: "name", // Assuming your data object has a 'user' field containing name, email, and image
        Cell: ({ row }) => (
          <div
            style={{
              width: "90%",
              display: "flex",
              flexDirection: "column",
            }}
          >
            {row.original.name}
          </div>
        ),
      },
      {
        Header: () => (
          <div className="flex items-center text-sm font-bold">
            <span>Create</span>
            {/* <SortIcon className="w-3 h-3 ml-5" /> */}
          </div>
        ),
        accessor: "create",
        Cell: ({ row }) =>
          row.original.name === "Report Management" ||
          row.original.name === "Logs Management" ||
          row.original.name === "Default Dashboard" ||
          row.original.name === "CDR Search" ? null : (
            <Field
              type="checkbox"
              style={{ accentColor: "#707070" }}
              name={"resources." + row.original.name}
              //   name={"resources."}
              value="create"
            />
          ),
      },

      {
        Header: () => (
          <div className="flex items-center text-sm font-bold">
            <span>View</span>
            {/* <SortIcon className="w-3 h-3 ml-5" /> */}
          </div>
        ),
        accessor: "view",
        Cell: ({ row }) => (
          // row.original.name === "Logs Management"?
          <Field
            disabled={
              row.original.name === "Logs Management" ||
              row.original.name === "Default Dashboard"
                ? true
                : false
            }
            type="checkbox"
            name={"resources." + row.original.name}
            value="view"
            style={{ accentColor: "#707070" }}
          />
        ),
      },
      {
        Header: () => (
          <div className="flex items-center text-sm font-bold">
            <span>Update</span>
            {/* <SortIcon className="w-3 h-3 ml-5" /> */}
          </div>
        ),
        accessor: "update",
        Cell: ({ row }) =>
          row.original.name === "Report Management" ||
          row.original.name === "Logs Management" ||
          row.original.name === "Default Dashboard" ||
          row.original.name === "CDR Search" ? null : (
            <Field
              style={{ accentColor: "#707070" }}
              type="checkbox"
              name={"resources." + row.original.name}
              value="update"
            />
          ),
      },
      {
        Header: () => (
          <div className="flex items-center text-sm font-bold">
            <span>Delete</span>
            {/* <SortIcon className="w-3 h-3 ml-5" /> */}
          </div>
        ),
        accessor: "delete",
        Cell: ({ row }) =>
          row.original.name === "Report Management" ||
          row.original.name === "Logs Management" ||
          row.original.name === "Default Dashboard" ||
          row.original.name === "CDR Search" ? null : (
            <Field
              style={{ accentColor: "#707070" }}
              type="checkbox"
              name={"resources." + row.original.name}
              value="delete"
            />
          ),
      },
      {
        Header: () => (
          <div className="flex items-center text-sm font-bold">
            <span>Download</span>
            {/* <SortIcon className="w-3 h-3 ml-5" /> */}
          </div>
        ),
        accessor: "download",
        Cell: ({ row }) =>
          row.original.name === "CDR Search" ? (
            <Field
              style={{ accentColor: "#707070" }}
              type="checkbox"
              name={"resources." + row.original.name}
              value="download"
            />
          ) : null,
      },
      {
        Header: () => (
          <div
            style={{
              display: "flex",
              width: "95px",
              justifyContent: "space-between",
              alignItems: "center",
              marginRight: "5px",
              marginLeft: "auto",
            }}
          >
            {" "}
            <div style={{ width: "70px", fontSize: "14px", fontWeight: 700 }}>
              Select all
            </div>
            <div className="mt-1">
              {" "}
              <CustomSwitch
                checked={selectAllObject.resources}
                //  value={user ? user.isUserActive : switchChecked}
                onChange={(checked, e) =>
                  handleSelectAll(checked, e, "resources")
                }
              />
            </div>
          </div>
        ),
        accessor: "toggle",
        Cell: ({ row }) =>
          row.original.name === "Dashboard Management" ? (
            <div className="flex justify-between w-full ">
              <div className="flex ">
                <InputLabel
                  label={" No. of Dashboard"}
                  isMandatory={true}
                  labelClassName={"whitespace-nowrap"}
                />
                <CssTooltip
                  title={`Max ${configApiData?.MAX_DASHBOARDS} dashboards allowed`}
                  placement="top"
                  arrow
                >
                  <InfoIcon className="ml-2 mt-1 w-4 h-3.5" />
                </CssTooltip>
              </div>
              <div className="flex ">
                <CssTooltip
                  title={
                    enableDashboardCount
                      ? `Enable dashboard create option to enable dashboard count`
                      : null
                  }
                  placement="top"
                  arrow
                >
                  <button
                    style={{
                      display: "flex",
                      width: "17px",
                      height: "20px",
                      border: "0.5px solid #9C9898",
                      margin: "0px 1px 0px",
                      borderRadius: "2px",
                      justifyContent: "center",
                      alignItems: "center",
                      lineHeight: "20px",
                      marginLeft: "10px",
                      cursor: enableDashboardCount ? "not-allowed" : "pointer",
                    }}
                    onClick={(e) => {
                      e.preventDefault();
                      if (dashBoardCount > 0) {
                        if (dashBoardCount === 1 && !enableDashboardCount) {
                          // setDashBoardCount(dashBoardCount)
                        } else {
                          setDashBoardCount(dashBoardCount - 1);
                        }
                      }
                      if (dashBoardCount === 0) {
                        setPanelCount(0);
                      }
                    }}
                    disabled={enableDashboardCount}
                  >
                    -
                  </button>{" "}
                </CssTooltip>

                <div
                  style={{
                    width: "29px",
                    height: "20px",
                    border: "0.5px solid #9C9898",
                    margin: "0px 1px 0px",
                    borderRadius: "2px",
                    lineHeight: "20px",
                    textAlign: "center",
                    cursor: enableDashboardCount ? "not-allowed" : "pointer",
                    // display:'flex'
                  }}
                  // type="number"
                  id="count"
                  name="count"
                  disabled={enableDashboardCount}
                  // value={dashBoardCount}
                  onChange={(e) => setDashBoardCount(parseInt(e.target.value))}
                >
                  {dashBoardCount}
                </div>
                <CssTooltip
                  title={
                    enableDashboardCount
                      ? `Enable dashboard create option to enable dashboard count`
                      : dashBoardCount === configApiData?.MAX_DASHBOARDS
                      ? `Max dashboard limit reached`
                      : null
                  }
                  placement="top"
                  arrow
                >
                  <button
                    style={{
                      display: "flex",
                      width: "16px",
                      height: "20px",
                      border: "0.5px solid #9C9898",
                      margin: "0px 1px 0px",
                      borderRadius: "2px",
                      justifyContent: "center",
                      alignItems: "center",
                      lineHeight: "20px",
                      cursor: enableDashboardCount ? "not-allowed" : "pointer",
                    }}
                    onClick={(e) => {
                      e.preventDefault();

                      if (dashBoardCount < configApiData?.MAX_DASHBOARDS) {
                        setDashBoardCount(dashBoardCount + 1);
                      }
                    }}
                    disabled={enableDashboardCount}
                  >
                    +
                  </button>{" "}
                </CssTooltip>
              </div>
            </div>
          ) : row.original.name === "Panel Management" ? (
            <div className="flex  justify-between w-full">
              <div className="flex ">
                <InputLabel
                  label={"No. of Panel Per Dashboard"}
                  isMandatory={true}
                  labelClassName={"whitespace-nowrap"}
                />{" "}
                <CssTooltip
                  title={`Max ${configApiData?.MAX_PANELS_PER_DASHBOARD} panels allowed`}
                  placement="top"
                  arrow
                >
                  <InfoIcon className="ml-2 mt-1 w-4 h-3.5" />
                </CssTooltip>
              </div>
              <div className="flex ">
                <button
                  style={{
                    display: "flex",
                    width: "17px",
                    height: "20px",
                    border: "0.5px solid #9C9898",
                    margin: "0px 1px 0px",
                    borderRadius: "2px",
                    justifyContent: "center",
                    alignItems: "center",
                    lineHeight: "20px",
                    marginLeft: "40px",
                    cursor:
                      dashBoardCount === 0 || enablePanelCount
                        ? "not-allowed"
                        : "pointer",
                  }}
                  disabled={dashBoardCount === 0 || enablePanelCount}
                  onClick={(e) => {
                    e.preventDefault();
                    if (panelCount > 0) {
                      if (
                        panelCount === 1 &&
                        !enableDashboardCount &&
                        !enablePanelCount
                      ) {
                        // setDashBoardCount(dashBoardCount)
                      } else {
                        setPanelCount(panelCount - 1);
                      }
                    }
                  }}
                >
                  -
                </button>
                <div
                  style={{
                    width: "29px",
                    height: "20px",
                    border: "0.5px solid #9C9898",
                    margin: "0px 1px 0px",
                    borderRadius: "2px",
                    lineHeight: "20px",
                    textAlign: "center",
                    cursor:
                      dashBoardCount === 0 || enablePanelCount
                        ? "not-allowed"
                        : "pointer",

                    // display:'flex'
                  }}
                  //  type="number"
                  id="count"
                  name="count"
                  disabled={dashBoardCount === 0 || enablePanelCount}
                  //value={panelCount}
                  onChange={(e) => setPanelCount(parseInt(e.target.value))}
                >
                  {dashBoardCount === 0 ? 0 : panelCount}
                </div>
                <CssTooltip
                  title={
                    enablePanelCount
                      ? `Enable dashboard & panel create option to enable panel count`
                      : panelCount === configApiData?.MAX_PANELS_PER_DASHBOARD
                      ? `Max panel limit reached`
                      : null
                  }
                  placement="top"
                  arrow
                >
                  <button
                    style={{
                      display: "flex",
                      width: "16px",
                      height: "20px",
                      border: "0.5px solid #9C9898",
                      margin: "0px 1px 0px",
                      borderRadius: "2px",
                      justifyContent: "center",
                      alignItems: "center",
                      lineHeight: "20px",
                      cursor:
                        dashBoardCount === 0 || enablePanelCount
                          ? "not-allowed"
                          : "pointer",
                    }}
                    onClick={(e) => {
                      e.preventDefault();
                      if (
                        panelCount < configApiData?.MAX_PANELS_PER_DASHBOARD
                      ) {
                        setPanelCount(panelCount + 1);
                      }
                    }}
                    disabled={dashBoardCount === 0 || enablePanelCount}
                  >
                    +
                  </button>
                </CssTooltip>
              </div>
            </div>
          ) : null,
      },
    ],
    [
      selectAllObject.resources,
      dashBoardCount,
      enableDashboardCount,
      panelCount,
      enablePanelCount,
    ]
  );

  const reportColumns = useMemo(
    () => [
      {
        Header: () => (
          <div className="flex items-center  text-sm font-bold">
            <span style={{ paddingLeft: "10px" }}>Report Name</span>
            {/* <SortIcon className="w-3 h-3 ml-5" /> */}
          </div>
        ),
        accessor: "name",
        Cell: ({ row }) => (
          <div
            style={{
              width: "90%",
              display: "flex",
              flexDirection: "column",
            }}
          >
            {row.original.name}
          </div>
        ),
      },
      {
        Header: () => (
          <div className="flex items-center  text-sm font-bold">
            <span>Download</span>
          </div>
        ),
        accessor: "download",
        Cell: ({ row }) => (
          <Field
            style={{ accentColor: "#707070" }}
            type="checkbox"
            name={"staticReports." + row.original.name}
            value="download"
            onClick={(e) => handleReportCheckboxClick(e)}
          />
        ),
      },

      {
        Header: () => (
          <div className="flex items-center  text-sm font-bold">
            <span>View</span>
          </div>
        ),
        accessor: "view",
        Cell: ({ row }) => (
          <Field
            style={{ accentColor: "#707070" }}
            type="checkbox"
            name={"staticReports." + row.original.name}
            value="view"
            onClick={(e) => handleReportCheckboxClick(e)}
          />
        ),
      },

      {
        Header: () => (
          <div
            style={{
              display: "flex",
              width: "95px",
              justifyContent: "space-between",
              alignItems: "center",
              marginRight: "5px",
              marginLeft: "auto",
            }}
          >
            {" "}
            <div style={{ width: "70px", fontSize: "14px", fontWeight: 700 }}>
              Select all
            </div>{" "}
            <div className="mt-1">
              <CustomSwitch
                checked={selectAllObject.reports}
                //  value={user ? user.isUserActive : switchChecked}
                onChange={(checked, e) =>
                  handleSelectAll(checked, e, "reports")
                }
              />
            </div>
          </div>
        ),
        accessor: "toggle",
      },
    ],
    [selectAllObject.reports, isReportPopup, reportArray]
  );

  const dashboardColumns = useMemo(
    () => [
      {
        Header: () => (
          <div className="flex items-center text-sm font-bold">
            <span style={{ paddingLeft: "10px" }}>Dynamic Dashboard</span>
            {/* <SortIcon className="w-3 h-3 ml-5" /> */}
          </div>
        ),
        accessor: "name",
        Cell: ({ row }) => (
          <div
            style={{
              width: "90%",
              display: "flex",
              flexDirection: "column",
            }}
          >
            {row.original.name}
          </div>
        ),
      },
      {
        Header: () => (
          <div className="flex items-center text-sm font-bold">
            <span>Download</span>
          </div>
        ),
        accessor: "download",
        Cell: ({ row }) => (
          <Field
            style={{ accentColor: "#707070" }}
            type="checkbox"
            name={"dynamicDashboard." + row.original.name}
            value="download"
            onClick={(e) => handleDashboardCheckboxClick(e)}
          />
        ),
      },
      {
        Header: () => (
          <div className="flex items-center text-sm font-bold">
            <span>View</span>
          </div>
        ),
        accessor: "view",
        Cell: ({ row }) => (
          <Field
            style={{ accentColor: "#707070" }}
            type="checkbox"
            name={"dynamicDashboard." + row.original.name}
            value="view"
            onClick={(e) => handleDashboardCheckboxClick(e)}
          />
        ),
      },
      {
        Header: () => (
          <div
            style={{
              display: "flex",
              width: "95px",
              justifyContent: "space-between",
              alignItems: "center",
              marginRight: "5px",
              marginLeft: "auto",
            }}
          >
            {" "}
            <div style={{ width: "70px", fontSize: "14px", fontWeight: 700 }}>
              Select all
            </div>
            <div className="mt-1">
              <CustomSwitch
                checked={selectAllObject.dashboard[dashboardPage]}
                //  value={user ? user.isUserActive : switchChecked}
                onChange={(checked, e) =>
                  handleSelectAll(checked, e, "dashboard")
                }
              />
            </div>
          </div>
        ),
        accessor: "toggle",
      },
    ],

    [selectAllObject.dashboard, isDashboardPopup, dashboardPage]
  );

  const getSlicedRows = (inputRows) => {
    return inputRows?.slice(dashboardPage * 10, dashboardPage * 10 + 10);
  };
  return (
    <div>
      <Formik
        key={initialValues.key}
        enableReinitialize={true}
        initialValues={initialValues}
        validationSchema={Yup.object({
          name: nameValidation.required("Role Name is Required"),

          // image: Yup.mixed().test("fileFormat", "Unsupprted file format of image uploaded", value => value && ['image/jpeg', "Image.png"].includes(value.type))
        })}
        validate={handleValidateCall}
        onSubmit={handleSubmit}
        innerRef={formikRef}
      >
        {({ values, setFieldValue }) => (
          <Form>
            <div className="bg-[white] mb-12 mt-7 p-8">
              <div className="flex flex-row ">
                <div className="flex flex-col w-4/5 ">
                  <InputLabel label={"Role Name"} isMandatory={true} />

                  <Field
                    name="name"
                    type="text"
                    // value={values.name}
                    //onChange={(event) => setFieldValue("name", values.name)}
                    className={style.fieldstyle}
                  />
                  <p className="text-errorColor text-xs my-2" valid={false}>
                    <ErrorMessage name="name" />
                  </p>
                </div>
              </div>
              <CustomTab
                tabs={tabItems}
                defaultTab={selectedTab}
                isLoading={isLoading}
                onChange={handleTabChange}
                backgroundColor="#fff"
                // color="#213871"
                color="#808080"
                selectedColor="#DC3833"
                isCreatePopup={isCreatePopup}
              />
              <div className="pt-4 "> </div>
              {selectedTab && selectedTab === 1 ? (
                <div>
                  <Table
                    columns={reportColumns}
                    data={Array.isArray(reportArray) ? reportArray : []}
                    isLoading={isLoading}
                  />
                </div>
              ) : selectedTab && selectedTab === 2 ? (
                <div className="mt-2">
                  <div>
                    <Table
                      columns={dashboardColumns}
                      data={getSlicedRows(
                        Array.isArray(dashboardArray) ? dashboardArray : []
                      )}
                      isLoading={isLoading}
                    />
                  </div>
                  {dashboardArray?.length > 0 ? (
                    <div className="flex justify-center my-4">
                      {" "}
                      <Pagination
                        className="pagination-bar"
                        currentPage={dashboardPage + 1}
                        totalCount={dashboardArray?.length}
                        pageSize={10}
                        onPageChange={(page) => {
                          setDashboardPage(page - 1);
                        }}
                      />
                    </div>
                  ) : null}{" "}
                </div>
              ) : (
                // : selectedTab && selectedTab === 3 ? (
                //   <div className="mt-2">
                //     <div>
                //       <Table
                //         columns={dashboardColumns}
                //         data={Array.isArray(dashboardArray) ? dashboardArray : []}
                //         isLoading={isLoading}
                //       />
                //     </div>
                //   </div>
                // )
                <div>
                  <Table
                    columns={resourceColumns}
                    // data={Array.isArray(sample) ? sample : []}
                    data={Array.isArray(resourceArray) ? resourceArray : []}
                    isLoading={isLoading}
                  />
                </div>
              )}

              <div class="flex justify-end my-5">
                {status && (
                  <p className="error" valid={false}>
                    {status}
                  </p>
                )}
                {selectedTab !== 0 ? (
                  <CancelButton
                    onClick={(e) => {
                      handleTabChange(e, selectedTab - 1);
                    }}
                    label={"Back"}
                    buttonClassName="w-[155px] h-10 text-xs mt-3"
                  ></CancelButton>
                ) : (
                  <CancelButton
                    onClick={() => {
                      navigate("/app/rolemanagement");
                    }}
                    label={"Cancel"}
                    buttonClassName="w-[155px] h-10 text-xs mt-3"
                  ></CancelButton>
                )}
                {selectedTab === 2 ? (
                  // <button
                  //   type="submit"
                  //   className="btn btn-danger w-[155px] h-12"
                  //   disabled={submitting}
                  // >
                  //   Save
                  // </button>
                  <Button
                    type="submit"
                    label={"Save"}
                    buttonClassName="w-[155px] h-10 text-xs ml-3 mt-3"
                    disabled={submitting}
                  ></Button>
                ) : (
                  <Button
                    onClick={(e) => {
                      handleTabChange(e, selectedTab + 1);
                    }}
                    label={"Next"}
                    buttonClassName="w-[155px] h-10 text-xs ml-3 mt-3"
                  ></Button>
                )}
              </div>
            </div>
          </Form>
        )}
      </Formik>
      <InfoModal
        icon={<Alert className="w-10 h-10 " />}
        show={showAlertConfirmation !== 0 ? true : false}
        onHide={() => setShowAlertConfirmation(0)}
        message={popupMessage}
      />
      <SuccessDialog
        show={successDialog}
        onHide={handleSuccessDialog}
        message={message}
      />
      <ToastContainer position="top-center" autoClose={3000} />
    </div>
  );
};

export default RolesForm;
