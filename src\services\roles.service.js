import axios from "axios";
import { config } from "../assets/config/config";
import getAPIMap from "../routes/ApiUrls";

const apiUrl = config.api.url;

export const roleService = {
  getAll,
  getById,
  create,
  // update,
  delete: _delete,
  getRoleDetails,
  getConfigData,
};

async function getAll(searchstr, pagenum = 1, limit = 10) {
  let url = getAPIMap("roles") + `?page=${pagenum}&limit=${limit}`;
  if (searchstr !== "" && searchstr !== undefined) {
    url += `&search=${searchstr}`;
  }
  return await axios.get(`${url}`);
}

async function getById(id) {
  return axios.get(getAPIMap("roles") + `/${id}`);
}

async function create(role, editRoleItem) {
  let apiData = {};
  apiData.name = role.name;
  apiData.description = role.name;
  apiData.dashboardCount = role.dashboardCount;
  apiData.panelCount = role.panelCount;
  apiData.resources = [];
  apiData.staticReports = [];
  apiData.dynamicReports = [];
  apiData.dynamicDashboard = Object.keys(role.dynamicDashboard).map((key) => ({
    name: key,
    permissions: {
      view: role.dynamicDashboard[key].includes("view") ? 1 : 0,
      download: role.dynamicDashboard[key].includes("download") ? 1 : 0,
    },
  }));
  // console.log(role.resources, role.staticReports);
  Object.keys(role.resources).forEach((eachKey) => {
    const eachObj = {};
    eachObj.name = eachKey;
    eachObj.permissions = {};
    eachObj.permissions.view =
      role.resources[eachKey].indexOf("view") > -1 ? 1 : 0;
    eachObj.permissions.update =
      role.resources[eachKey].indexOf("update") > -1 ? 1 : 0;
    eachObj.permissions.delete =
      role.resources[eachKey].indexOf("delete") > -1 ? 1 : 0;
    eachObj.permissions.create =
      role.resources[eachKey].indexOf("create") > -1 ? 1 : 0;
    eachObj.permissions.download =
      role.resources[eachKey].indexOf("download") > -1 ? 1 : 0;
    // console.log(eachObj);
    apiData.resources.push(eachObj);
  });
  Object.keys(role.staticReports).forEach((eachKey) => {
    const eachObj = {};
    eachObj.name = eachKey;
    eachObj.permissions = {};
    eachObj.permissions.view =
      role.staticReports[eachKey].indexOf("view") > -1 ? 1 : 0;
    eachObj.permissions.download =
      role.staticReports[eachKey].indexOf("download") > -1 ? 1 : 0;
    apiData.staticReports.push(eachObj);
  });
  // console.log("Role API->: ", apiData);
  if (editRoleItem) {
    return await axios.put(getAPIMap("roles") + `/${editRoleItem}`, apiData);
    // return await axios.put(`${apiUrl}/v1/roles/`, apiData, editRoleItem);
  } else {
    return await axios.post(getAPIMap("roles"), apiData);
  }
  //  return fetchWrapper.post(baseUrl, params);
}
//console.log("no format", role);

// async function update(id, role) {
//   return await axios.patch(`${apiUrl}/v1/roles/${id}`, role);
//   // return fetchWrapper.put(`${baseUrl}/${id}`, params);
// }

// prefixed with underscored because delete is a reserved word in javascript
async function _delete(id) {
  return await axios.delete(getAPIMap("roles") + `/${id}`);
}
async function getRoleDetails() {
  return axios.get(getAPIMap("roles"));
}

async function getConfigData() {
  return await axios.get(getAPIMap("configuration"));
}
